import crypto from 'crypto'
import { NextRequest, NextResponse } from 'next/server'

interface InterServerAuthConfig {
  validApiKeys: string[]
  secretKey: string
  maxTimestampAge?: number // in milliseconds, default 5 minutes
}

interface AuthenticatedRequest extends NextRequest {
  serverSource?: string
  apiKey?: string
}

export function createInterServerAuthMiddleware(config: InterServerAuthConfig) {
  const maxAge = config.maxTimestampAge || 5 * 60 * 1000 // 5 minutes

  return async function interServerAuth(
    request: NextRequest
  ): Promise<NextResponse | null> {
    try {
      // Extract headers
      const apiKey = request.headers.get('X-API-Key')
      const serverSource = request.headers.get('X-Server-Source')
      const signature = request.headers.get('X-Signature')
      const timestamp = request.headers.get('X-Timestamp')

      // Validate required headers
      if (!apiKey || !serverSource || !signature || !timestamp) {
        return NextResponse.json(
          { error: 'Missing required authentication headers' },
          { status: 401 }
        )
      }

      // Validate API key
      if (!config.validApiKeys.includes(apiKey)) {
        return NextResponse.json(
          { error: 'Invalid API key' },
          { status: 401 }
        )
      }

      // Validate server source
      const validSources = ['STAFF', 'STUDENTS', 'TESTS', 'IELTS']
      if (!validSources.includes(serverSource)) {
        return NextResponse.json(
          { error: 'Invalid server source' },
          { status: 400 }
        )
      }

      // Validate timestamp (prevent replay attacks)
      const requestTime = new Date(timestamp).getTime()
      const currentTime = Date.now()
      
      if (isNaN(requestTime) || Math.abs(currentTime - requestTime) > maxAge) {
        return NextResponse.json(
          { error: 'Request timestamp is invalid or too old' },
          { status: 401 }
        )
      }

      // Validate signature
      const method = request.method
      const path = new URL(request.url).pathname
      const body = request.method !== 'GET' ? await request.text() : ''
      
      const expectedSignature = generateSignature(method, path, body, timestamp, config.secretKey)
      
      if (!crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      )) {
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 401 }
        )
      }

      // Add server info to request for downstream handlers
      const authenticatedRequest = request as AuthenticatedRequest
      authenticatedRequest.serverSource = serverSource
      authenticatedRequest.apiKey = apiKey

      // Authentication successful, continue to next handler
      return null
    } catch (error) {
      console.error('Inter-server authentication error:', error)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      )
    }
  }
}

function generateSignature(
  method: string,
  path: string,
  body: string,
  timestamp: string,
  secretKey: string
): string {
  const message = `${method}|${path}|${body}|${timestamp}`
  return crypto
    .createHmac('sha256', secretKey)
    .update(message)
    .digest('hex')
}

// Rate limiting for inter-server requests
interface RateLimitConfig {
  windowMs: number // time window in milliseconds
  maxRequests: number // max requests per window
}

const requestCounts = new Map<string, { count: number; resetTime: number }>()

export function createRateLimitMiddleware(config: RateLimitConfig) {
  return function rateLimit(request: NextRequest): NextResponse | null {
    const clientId = request.headers.get('X-API-Key') || request.ip || 'unknown'
    const now = Date.now()
    
    const clientData = requestCounts.get(clientId)
    
    if (!clientData || now > clientData.resetTime) {
      // Reset or initialize counter
      requestCounts.set(clientId, {
        count: 1,
        resetTime: now + config.windowMs
      })
      return null
    }
    
    if (clientData.count >= config.maxRequests) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
        },
        { 
          status: 429,
          headers: {
            'Retry-After': Math.ceil((clientData.resetTime - now) / 1000).toString()
          }
        }
      )
    }
    
    clientData.count++
    return null
  }
}

// Logging middleware for inter-server requests
export function createLoggingMiddleware() {
  return function logInterServerRequest(request: NextRequest): NextResponse | null {
    const serverSource = request.headers.get('X-Server-Source')
    const timestamp = request.headers.get('X-Timestamp')
    const path = new URL(request.url).pathname
    
    console.log(`[Inter-Server] ${serverSource} -> ${request.method} ${path} at ${timestamp}`)
    
    return null
  }
}

// Combine multiple middleware functions
export function combineMiddleware(...middlewares: Array<(req: NextRequest) => Promise<NextResponse | null> | NextResponse | null>) {
  return async function combinedMiddleware(request: NextRequest): Promise<NextResponse | null> {
    for (const middleware of middlewares) {
      const result = await middleware(request)
      if (result) {
        return result // Stop on first middleware that returns a response
      }
    }
    return null
  }
}
