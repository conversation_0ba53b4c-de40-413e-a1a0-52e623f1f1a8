import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string
      email?: string
      phone: string
      role: string
      studentProfile?: any
    }
  }

  interface User {
    id: string
    name: string
    email?: string
    phone: string
    role: string
    studentProfile?: any
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    studentProfile?: any
  }
}
