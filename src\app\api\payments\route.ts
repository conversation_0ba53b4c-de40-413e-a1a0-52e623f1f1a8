import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "STUDENT") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the student's profile
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        studentProfile: true
      }
    })

    if (!user?.studentProfile) {
      return NextResponse.json({ error: "Student profile not found" }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const status = searchParams.get("status") || ""

    const skip = (page - 1) * limit

    const where = {
      studentId: user.studentProfile.id,
      ...(status && { status })
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" }
      }),
      prisma.payment.count({ where })
    ])

    // Calculate summary
    const summary = await prisma.payment.aggregate({
      where: { studentId: user.studentProfile.id },
      _sum: {
        amount: true
      }
    })

    const statusBreakdown = await prisma.payment.groupBy({
      by: ['status'],
      where: { studentId: user.studentProfile.id },
      _sum: {
        amount: true
      },
      _count: {
        _all: true
      }
    })

    // Get upcoming due payments
    const upcomingPayments = await prisma.payment.findMany({
      where: {
        studentId: user.studentProfile.id,
        status: "DEBT",
        dueDate: {
          gte: new Date(),
          lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        }
      },
      orderBy: { dueDate: "asc" },
      take: 5
    })

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        totalPaid: summary._sum.amount || 0,
        statusBreakdown,
        upcomingPayments
      }
    })
  } catch (error) {
    console.error("Error fetching student payments:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
