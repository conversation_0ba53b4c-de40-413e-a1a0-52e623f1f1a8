import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createAssessmentSchema = z.object({
  studentId: z.string().min(1, "Student is required"),
  groupReferenceId: z.string().optional(),
  testName: z.string().min(1, "Test name is required"),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").default(100),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "text", "essay", "listening", "speaking"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
    points: z.number().default(1)
  })).optional(),
  assignedBy: z.string().optional(),
  assignedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get("studentId")
    const type = searchParams.get("type")
    const level = searchParams.get("level")
    const status = searchParams.get("status") // pending, completed, graded
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    let where: any = {}

    // For students, only show their own assessments
    if (session.user.role === "STUDENT") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { studentProfile: true }
      })

      if (!user?.studentProfile) {
        return NextResponse.json({ error: "Student profile not found" }, { status: 404 })
      }

      where.studentId = user.studentProfile.id
    } else if (studentId) {
      where.studentId = studentId
    }

    if (type) {
      where.type = type
    }

    if (level) {
      where.level = level
    }

    if (status === "pending") {
      where.startedAt = null
    } else if (status === "completed") {
      where.completedAt = { not: null }
      where.score = null
    } else if (status === "graded") {
      where.score = { not: null }
    }

    const [assessments, total] = await Promise.all([
      prisma.assessment.findMany({
        where,
        skip,
        take: limit,
        include: {
          student: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            }
          },
          groupReference: true
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.assessment.count({ where })
    ])

    return NextResponse.json({
      assessments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching assessments:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAssessmentSchema.parse(body)

    // Verify student exists
    const student = await prisma.student.findUnique({
      where: { id: validatedData.studentId }
    })

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    // Verify group reference exists if provided
    if (validatedData.groupReferenceId) {
      const groupReference = await prisma.groupReference.findUnique({
        where: { id: validatedData.groupReferenceId }
      })

      if (!groupReference) {
        return NextResponse.json({ error: "Group not found" }, { status: 404 })
      }
    }

    // Create assessment
    const assessment = await prisma.assessment.create({
      data: {
        studentId: validatedData.studentId,
        groupReferenceId: validatedData.groupReferenceId,
        testName: validatedData.testName,
        type: validatedData.type,
        level: validatedData.level,
        maxScore: validatedData.maxScore,
        questions: validatedData.questions,
        assignedBy: validatedData.assignedBy,
        assignedAt: validatedData.assignedAt,
      },
      include: {
        student: {
          include: {
            user: {
              select: { name: true, email: true }
            }
          }
        },
        groupReference: true
      }
    })

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
