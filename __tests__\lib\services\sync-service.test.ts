import { SyncService } from '@/lib/services/sync-service'

// Mock the staff client
jest.mock('@/lib/api-clients/staff-client', () => ({
  getStaffClient: jest.fn(() => ({
    getGroupReferences: jest.fn(),
    getTeacherReferences: jest.fn(),
    notifyStudentUpdate: jest.fn(),
    notifyPaymentReceived: jest.fn(),
    notifyAttendanceMarked: jest.fn(),
    healthCheck: jest.fn()
  }))
}))

import { getStaffClient } from '@/lib/api-clients/staff-client'

describe('SyncService', () => {
  let syncService: SyncService
  let mockStaffClient: jest.Mocked<ReturnType<typeof getStaffClient>>

  beforeEach(() => {
    syncService = new SyncService()
    mockStaffClient = getStaffClient() as jest.Mocked<ReturnType<typeof getStaffClient>>
    jest.clearAllMocks()
  })

  describe('syncGroupReferences', () => {
    it('should sync group references successfully', async () => {
      const mockGroups = [
        {
          id: 'group-1',
          name: 'B1 Morning Group',
          teacherReferenceId: 'teacher-1',
          courseName: 'General English B1',
          schedule: { days: ['MON', 'WED', 'FRI'], time: '09:00' },
          room: 'Room 101',
          branch: 'main',
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-06-01T00:00:00.000Z',
          isActive: true
        },
        {
          id: 'group-2',
          name: 'A2 Evening Group',
          teacherReferenceId: 'teacher-2',
          courseName: 'General English A2',
          schedule: { days: ['TUE', 'THU'], time: '18:00' },
          room: 'Room 102',
          branch: 'main',
          startDate: '2024-01-15T00:00:00.000Z',
          endDate: '2024-06-15T00:00:00.000Z',
          isActive: true
        }
      ]

      mockStaffClient.getGroupReferences.mockResolvedValueOnce({
        success: true,
        data: mockGroups
      })

      const result = await syncService.syncGroupReferences()

      expect(result.success).toBe(true)
      expect(result.synced).toBe(2)
      expect(result.errors).toHaveLength(0)
      expect(mockStaffClient.getGroupReferences).toHaveBeenCalledTimes(1)
    })

    it('should handle API errors when syncing groups', async () => {
      mockStaffClient.getGroupReferences.mockResolvedValueOnce({
        success: false,
        error: 'Staff server unavailable'
      })

      const result = await syncService.syncGroupReferences()

      expect(result.success).toBe(false)
      expect(result.synced).toBe(0)
      expect(result.errors).toContain('Staff server unavailable')
    })

    it('should handle network errors when syncing groups', async () => {
      mockStaffClient.getGroupReferences.mockRejectedValueOnce(
        new Error('Network error')
      )

      const result = await syncService.syncGroupReferences()

      expect(result.success).toBe(false)
      expect(result.synced).toBe(0)
      expect(result.errors).toContain('Network error')
    })
  })

  describe('syncTeacherReferences', () => {
    it('should sync teacher references successfully', async () => {
      const mockTeachers = [
        {
          id: 'teacher-1',
          name: 'Alice Johnson',
          subject: 'English',
          branch: 'main',
          photoUrl: '/images/teachers/alice.jpg'
        },
        {
          id: 'teacher-2',
          name: 'Bob Wilson',
          subject: 'English',
          branch: 'main',
          photoUrl: '/images/teachers/bob.jpg'
        }
      ]

      mockStaffClient.getTeacherReferences.mockResolvedValueOnce({
        success: true,
        data: mockTeachers
      })

      const result = await syncService.syncTeacherReferences()

      expect(result.success).toBe(true)
      expect(result.synced).toBe(2)
      expect(result.errors).toHaveLength(0)
      expect(mockStaffClient.getTeacherReferences).toHaveBeenCalledTimes(1)
    })

    it('should handle API errors when syncing teachers', async () => {
      mockStaffClient.getTeacherReferences.mockResolvedValueOnce({
        success: false,
        error: 'Unauthorized access'
      })

      const result = await syncService.syncTeacherReferences()

      expect(result.success).toBe(false)
      expect(result.synced).toBe(0)
      expect(result.errors).toContain('Unauthorized access')
    })
  })

  describe('notifyStudentUpdate', () => {
    it('should notify staff server of student updates', async () => {
      mockStaffClient.notifyStudentUpdate.mockResolvedValueOnce({
        success: true
      })

      const result = await syncService.notifyStudentUpdate(
        'student-1',
        'UPDATED',
        { name: 'New Name' }
      )

      expect(result).toBe(true)
      expect(mockStaffClient.notifyStudentUpdate).toHaveBeenCalledWith({
        studentId: 'student-1',
        action: 'UPDATED',
        changes: { name: 'New Name' }
      })
    })

    it('should handle notification failures gracefully', async () => {
      mockStaffClient.notifyStudentUpdate.mockRejectedValueOnce(
        new Error('Network error')
      )

      const result = await syncService.notifyStudentUpdate(
        'student-1',
        'CREATED'
      )

      expect(result).toBe(false)
    })
  })

  describe('notifyPaymentReceived', () => {
    it('should notify staff server of payment receipts', async () => {
      const paymentData = {
        studentId: 'student-1',
        paymentId: 'payment-1',
        amount: 500000,
        method: 'CASH',
        paidDate: '2024-01-15T00:00:00.000Z'
      }

      mockStaffClient.notifyPaymentReceived.mockResolvedValueOnce({
        success: true
      })

      const result = await syncService.notifyPaymentReceived(paymentData)

      expect(result).toBe(true)
      expect(mockStaffClient.notifyPaymentReceived).toHaveBeenCalledWith(paymentData)
    })

    it('should handle payment notification failures', async () => {
      const paymentData = {
        studentId: 'student-1',
        paymentId: 'payment-1',
        amount: 500000,
        method: 'CASH',
        paidDate: '2024-01-15T00:00:00.000Z'
      }

      mockStaffClient.notifyPaymentReceived.mockRejectedValueOnce(
        new Error('Server error')
      )

      const result = await syncService.notifyPaymentReceived(paymentData)

      expect(result).toBe(false)
    })
  })

  describe('notifyAttendanceMarked', () => {
    it('should notify staff server of attendance records', async () => {
      const attendanceData = {
        studentId: 'student-1',
        classReferenceId: 'class-1',
        status: 'PRESENT',
        date: '2024-01-15T09:00:00.000Z'
      }

      mockStaffClient.notifyAttendanceMarked.mockResolvedValueOnce({
        success: true
      })

      const result = await syncService.notifyAttendanceMarked(attendanceData)

      expect(result).toBe(true)
      expect(mockStaffClient.notifyAttendanceMarked).toHaveBeenCalledWith(attendanceData)
    })
  })

  describe('fullSync', () => {
    it('should perform full synchronization successfully', async () => {
      mockStaffClient.getGroupReferences.mockResolvedValueOnce({
        success: true,
        data: [{ id: 'group-1', name: 'Test Group' }]
      })

      mockStaffClient.getTeacherReferences.mockResolvedValueOnce({
        success: true,
        data: [{ id: 'teacher-1', name: 'Test Teacher' }]
      })

      const result = await syncService.fullSync()

      expect(result.overall.success).toBe(true)
      expect(result.overall.totalSynced).toBe(2)
      expect(result.overall.totalErrors).toBe(0)
      expect(result.groups.success).toBe(true)
      expect(result.teachers.success).toBe(true)
    })

    it('should report overall failure if any sync fails', async () => {
      mockStaffClient.getGroupReferences.mockResolvedValueOnce({
        success: true,
        data: []
      })

      mockStaffClient.getTeacherReferences.mockResolvedValueOnce({
        success: false,
        error: 'Server error'
      })

      const result = await syncService.fullSync()

      expect(result.overall.success).toBe(false)
      expect(result.overall.totalErrors).toBeGreaterThan(0)
      expect(result.groups.success).toBe(true)
      expect(result.teachers.success).toBe(false)
    })
  })

  describe('validateSyncIntegrity', () => {
    it('should validate sync integrity successfully', async () => {
      mockStaffClient.healthCheck.mockResolvedValueOnce({
        success: true,
        data: { status: 'healthy', timestamp: '2024-01-01T00:00:00.000Z' }
      })

      const result = await syncService.validateSyncIntegrity()

      expect(result.valid).toBe(true)
      expect(result.issues).toHaveLength(0)
      expect(mockStaffClient.healthCheck).toHaveBeenCalledTimes(1)
    })

    it('should detect staff server connectivity issues', async () => {
      mockStaffClient.healthCheck.mockResolvedValueOnce({
        success: false,
        error: 'Server unreachable'
      })

      const result = await syncService.validateSyncIntegrity()

      expect(result.valid).toBe(false)
      expect(result.issues).toContain('Staff server is not reachable')
      expect(result.recommendations).toContain('Check network connectivity and server status')
    })

    it('should handle health check errors', async () => {
      mockStaffClient.healthCheck.mockRejectedValueOnce(
        new Error('Network timeout')
      )

      const result = await syncService.validateSyncIntegrity()

      expect(result.valid).toBe(false)
      expect(result.issues).toContain('Network timeout')
    })
  })
})
