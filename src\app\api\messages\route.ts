import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "STUDENT") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const priority = searchParams.get("priority")
    const status = searchParams.get("status")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Get the student's profile
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { studentProfile: true }
    })

    if (!user?.studentProfile) {
      return NextResponse.json({ error: "Student profile not found" }, { status: 404 })
    }

    let where: any = {
      OR: [
        { recipientType: "ALL" },
        { recipientType: "STUDENTS" },
        { 
          AND: [
            { recipientType: "SPECIFIC" },
            { recipientIds: { has: user.studentProfile.id } }
          ]
        }
      ],
      status: "SENT" // Only show sent messages to students
    }

    if (priority) {
      where.priority = priority
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        skip,
        take: limit,
        select: {
          id: true,
          subject: true,
          content: true,
          priority: true,
          sentAt: true,
          createdAt: true,
          senderReferenceId: true,
          // Don't include recipient details for privacy
        },
        orderBy: { sentAt: "desc" }
      }),
      prisma.message.count({ where })
    ])

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching messages:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
