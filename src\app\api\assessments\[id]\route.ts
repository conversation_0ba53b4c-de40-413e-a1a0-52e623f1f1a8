import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const updateAssessmentSchema = z.object({
  testName: z.string().min(1, "Test name is required").optional(),
  type: z.enum(["LEVEL_TEST", "PROGRESS_TEST", "FINAL_EXAM", "GROUP_TEST"]).optional(),
  level: z.enum(["A1", "A2", "B1", "B2", "IELTS", "SAT", "MATH", "KIDS"]).optional(),
  maxScore: z.number().min(1, "Max score must be at least 1").optional(),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "text", "essay", "listening", "speaking"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
    points: z.number().default(1)
  })).optional(),
})

const submitAnswersSchema = z.object({
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.string(),
    timeSpent: z.number().optional() // in seconds
  })),
  startedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
  completedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

const gradeAssessmentSchema = z.object({
  score: z.number().min(0, "Score must be non-negative"),
  passed: z.boolean().optional(),
  results: z.record(z.any()).optional(),
  feedback: z.string().optional(),
  gradedBy: z.string().optional(),
  gradedAt: z.string().optional().transform((str) => str ? new Date(str) : new Date()),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const assessment = await prisma.assessment.findUnique({
      where: { id: params.id },
      include: {
        student: {
          include: {
            user: {
              select: { name: true, email: true }
            }
          }
        },
        groupReference: true
      }
    })

    if (!assessment) {
      return NextResponse.json({ error: "Assessment not found" }, { status: 404 })
    }

    // For students, only allow access to their own assessments
    if (session.user.role === "STUDENT") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { studentProfile: true }
      })

      if (!user?.studentProfile || assessment.studentId !== user.studentProfile.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
      }
    }

    return NextResponse.json(assessment)
  } catch (error) {
    console.error("Error fetching assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const action = body.action // 'update', 'submit_answers', 'grade'

    const assessment = await prisma.assessment.findUnique({
      where: { id: params.id },
      include: { student: true }
    })

    if (!assessment) {
      return NextResponse.json({ error: "Assessment not found" }, { status: 404 })
    }

    let updatedAssessment

    if (action === "submit_answers") {
      // Student submitting answers
      if (session.user.role !== "STUDENT") {
        return NextResponse.json({ error: "Only students can submit answers" }, { status: 403 })
      }

      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { studentProfile: true }
      })

      if (!user?.studentProfile || assessment.studentId !== user.studentProfile.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
      }

      const validatedData = submitAnswersSchema.parse(body)

      updatedAssessment = await prisma.assessment.update({
        where: { id: params.id },
        data: {
          results: { answers: validatedData.answers },
          startedAt: validatedData.startedAt,
          completedAt: validatedData.completedAt,
        },
        include: {
          student: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            }
          },
          groupReference: true
        }
      })
    } else if (action === "grade") {
      // Teacher/Admin grading assessment
      const validatedData = gradeAssessmentSchema.parse(body)

      // Calculate pass/fail if not provided
      if (validatedData.passed === undefined) {
        const passingScore = 60
        const percentage = (validatedData.score / assessment.maxScore) * 100
        validatedData.passed = percentage >= passingScore
      }

      updatedAssessment = await prisma.assessment.update({
        where: { id: params.id },
        data: {
          score: validatedData.score,
          passed: validatedData.passed,
          results: {
            ...assessment.results as any,
            ...validatedData.results,
            feedback: validatedData.feedback
          },
        },
        include: {
          student: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            }
          },
          groupReference: true
        }
      })
    } else {
      // Update assessment details
      const validatedData = updateAssessmentSchema.parse(body)

      updatedAssessment = await prisma.assessment.update({
        where: { id: params.id },
        data: validatedData,
        include: {
          student: {
            include: {
              user: {
                select: { name: true, email: true }
              }
            }
          },
          groupReference: true
        }
      })
    }

    return NextResponse.json(updatedAssessment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error updating assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await prisma.assessment.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Assessment deleted successfully" })
  } catch (error) {
    console.error("Error deleting assessment:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
