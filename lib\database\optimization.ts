// Database optimization utilities for the students portal

interface IndexRecommendation {
  table: string
  columns: string[]
  type: 'btree' | 'hash' | 'gin' | 'gist'
  reason: string
  priority: 'high' | 'medium' | 'low'
}

interface QueryOptimization {
  query: string
  optimization: string
  impact: 'high' | 'medium' | 'low'
  description: string
}

export class DatabaseOptimizer {
  
  // Get recommended indexes for better performance
  getIndexRecommendations(): IndexRecommendation[] {
    return [
      {
        table: 'students',
        columns: ['userId'],
        type: 'btree',
        reason: 'Frequent user-to-student lookups',
        priority: 'high'
      },
      {
        table: 'students',
        columns: ['status', 'branch'],
        type: 'btree',
        reason: 'Student filtering by status and branch',
        priority: 'high'
      },
      {
        table: 'students',
        columns: ['currentGroupReferenceId'],
        type: 'btree',
        reason: 'Group membership queries',
        priority: 'high'
      },
      {
        table: 'enrollments',
        columns: ['studentId', 'status'],
        type: 'btree',
        reason: 'Student enrollment history',
        priority: 'high'
      },
      {
        table: 'enrollments',
        columns: ['groupReferenceId', 'status'],
        type: 'btree',
        reason: 'Group enrollment queries',
        priority: 'medium'
      },
      {
        table: 'payments',
        columns: ['studentId', 'status'],
        type: 'btree',
        reason: 'Student payment history',
        priority: 'high'
      },
      {
        table: 'payments',
        columns: ['paidDate'],
        type: 'btree',
        reason: 'Payment date queries',
        priority: 'medium'
      },
      {
        table: 'attendances',
        columns: ['studentId', 'groupReferenceId'],
        type: 'btree',
        reason: 'Attendance tracking queries',
        priority: 'high'
      },
      {
        table: 'attendances',
        columns: ['createdAt'],
        type: 'btree',
        reason: 'Date-based attendance reports',
        priority: 'medium'
      },
      {
        table: 'assessments',
        columns: ['studentId', 'type'],
        type: 'btree',
        reason: 'Student assessment queries',
        priority: 'high'
      },
      {
        table: 'assessments',
        columns: ['groupReferenceId', 'type'],
        type: 'btree',
        reason: 'Group assessment queries',
        priority: 'medium'
      },
      {
        table: 'group_references',
        columns: ['branch', 'isActive'],
        type: 'btree',
        reason: 'Active group filtering',
        priority: 'high'
      },
      {
        table: 'group_references',
        columns: ['teacherReferenceId'],
        type: 'btree',
        reason: 'Teacher-group relationships',
        priority: 'medium'
      }
    ]
  }

  // Get SQL statements to create recommended indexes
  getIndexCreationSQL(): string[] {
    const recommendations = this.getIndexRecommendations()
    
    return recommendations.map(rec => {
      const indexName = `idx_${rec.table}_${rec.columns.join('_')}`
      const columns = rec.columns.join(', ')
      
      return `CREATE INDEX CONCURRENTLY IF NOT EXISTS ${indexName} ON ${rec.table} USING ${rec.type} (${columns});`
    })
  }

  // Get query optimizations
  getQueryOptimizations(): QueryOptimization[] {
    return [
      {
        query: 'SELECT * FROM students WHERE status = ? AND branch = ?',
        optimization: 'SELECT id, userId, level, status, branch FROM students WHERE status = ? AND branch = ?',
        impact: 'medium',
        description: 'Avoid SELECT * and only fetch needed columns'
      },
      {
        query: 'Complex queries with multiple JOINs for dashboard',
        optimization: 'Use materialized views for dashboard data',
        impact: 'high',
        description: 'Pre-calculate dashboard statistics and refresh periodically'
      },
      {
        query: 'Attendance queries across date ranges',
        optimization: 'Use date partitioning for attendance table',
        impact: 'medium',
        description: 'Partition attendance by month for better performance'
      },
      {
        query: 'Assessment result aggregations',
        optimization: 'Cache assessment statistics in summary tables',
        impact: 'medium',
        description: 'Pre-calculate progress statistics for faster loading'
      },
      {
        query: 'Group reference synchronization queries',
        optimization: 'Use lastSyncedAt index for incremental sync',
        impact: 'high',
        description: 'Only sync records modified since last sync'
      }
    ]
  }

  // Database maintenance recommendations
  getMaintenanceRecommendations(): string[] {
    return [
      'Run VACUUM ANALYZE weekly to update statistics and reclaim space',
      'Monitor slow query log and optimize queries taking >500ms',
      'Set up connection pooling optimized for student portal usage patterns',
      'Configure read replicas for reporting and analytics queries',
      'Implement automated backup with 30-day retention',
      'Monitor PWA offline data synchronization performance',
      'Set up alerts for database connection limits',
      'Regular cleanup of old assessment data and temporary files',
      'Monitor storage usage for user-uploaded content',
      'Implement proper indexing for full-text search if needed'
    ]
  }

  // Performance monitoring queries
  getPerformanceQueries(): Record<string, string> {
    return {
      slowQueries: `
        SELECT query, calls, total_time, mean_time, rows
        FROM pg_stat_statements
        WHERE mean_time > 500
        ORDER BY mean_time DESC
        LIMIT 10;
      `,
      
      studentActivityStats: `
        SELECT 
          DATE_TRUNC('hour', createdAt) as hour,
          COUNT(*) as activity_count
        FROM attendances
        WHERE createdAt > NOW() - INTERVAL '24 hours'
        GROUP BY hour
        ORDER BY hour;
      `,
      
      syncPerformance: `
        SELECT 
          'group_references' as table_name,
          COUNT(*) as total_records,
          COUNT(*) FILTER (WHERE lastSyncedAt > NOW() - INTERVAL '1 hour') as recently_synced,
          MAX(lastSyncedAt) as last_sync
        FROM group_references
        UNION ALL
        SELECT 
          'teacher_references' as table_name,
          COUNT(*) as total_records,
          COUNT(*) FILTER (WHERE lastSyncedAt > NOW() - INTERVAL '1 hour') as recently_synced,
          MAX(lastSyncedAt) as last_sync
        FROM teacher_references;
      `,
      
      assessmentStats: `
        SELECT 
          type,
          COUNT(*) as total_assessments,
          COUNT(*) FILTER (WHERE completedAt IS NOT NULL) as completed,
          AVG(score) FILTER (WHERE score IS NOT NULL) as avg_score
        FROM assessments
        WHERE createdAt > NOW() - INTERVAL '30 days'
        GROUP BY type;
      `,
      
      paymentTrends: `
        SELECT 
          DATE_TRUNC('month', paidDate) as month,
          COUNT(*) as payment_count,
          SUM(amount) as total_amount,
          method
        FROM payments
        WHERE paidDate > NOW() - INTERVAL '12 months'
        GROUP BY month, method
        ORDER BY month DESC;
      `
    }
  }

  // Data archival recommendations
  getArchivalStrategy(): {
    table: string
    criteria: string
    retention: string
    method: string
  }[] {
    return [
      {
        table: 'attendances',
        criteria: 'createdAt < NOW() - INTERVAL \'2 years\'',
        retention: '2 years',
        method: 'Archive to cold storage'
      },
      {
        table: 'assessments',
        criteria: 'completedAt < NOW() - INTERVAL \'3 years\' AND type != \'FINAL_EXAM\'',
        retention: '3 years (keep final exams longer)',
        method: 'Archive non-critical assessments'
      },
      {
        table: 'payments',
        criteria: 'createdAt < NOW() - INTERVAL \'7 years\'',
        retention: '7 years (legal requirement)',
        method: 'Archive with encryption'
      },
      {
        table: 'messages',
        criteria: 'createdAt < NOW() - INTERVAL \'1 year\'',
        retention: '1 year',
        method: 'Archive or delete old messages'
      }
    ]
  }

  // PWA-specific optimizations
  getPWAOptimizations(): {
    feature: string
    optimization: string
    impact: string
  }[] {
    return [
      {
        feature: 'Offline Data Sync',
        optimization: 'Implement incremental sync based on lastSyncedAt timestamps',
        impact: 'Reduces data transfer and sync time'
      },
      {
        feature: 'Service Worker Cache',
        optimization: 'Cache frequently accessed student data and group information',
        impact: 'Improves offline experience and reduces server load'
      },
      {
        feature: 'Background Sync',
        optimization: 'Queue attendance and assessment submissions for background sync',
        impact: 'Better user experience during poor connectivity'
      },
      {
        feature: 'Data Compression',
        optimization: 'Compress API responses for mobile data efficiency',
        impact: 'Reduces mobile data usage and improves performance'
      }
    ]
  }
}

// Singleton instance
let optimizer: DatabaseOptimizer | null = null

export function getDatabaseOptimizer(): DatabaseOptimizer {
  if (!optimizer) {
    optimizer = new DatabaseOptimizer()
  }
  return optimizer
}
