"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Server, 
  Database,
  AlertTriangle,
  Activity,
  Sync
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"

interface SyncLog {
  id: string
  resource: string
  action: string
  recordCount: number
  success: boolean
  errors: string[] | null
  lastSyncTime: string
  createdAt: string
}

interface SyncStats {
  resource: string
  _count: { _all: number }
  _max: { lastSyncTime: string | null }
}

interface SyncStatus {
  recentSyncLogs: SyncLog[]
  syncStats: SyncStats[]
  staffServerStatus: any
  lastChecked: string
}

export default function SyncManagementPage() {
  const { data: session } = useSession()
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)
  const [syncingResource, setSyncingResource] = useState<string | null>(null)

  const fetchSyncStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/sync")
      if (response.ok) {
        const data = await response.json()
        setSyncStatus(data)
      }
    } catch (error) {
      console.error("Error fetching sync status:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSyncStatus()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSyncStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  const triggerSync = async (resource: "students" | "announcements" | "all") => {
    try {
      setSyncing(true)
      setSyncingResource(resource)
      
      const response = await fetch("/api/sync", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ resource }),
      })

      if (response.ok) {
        const result = await response.json()
        console.log("Sync result:", result)
        
        // Refresh status after sync
        setTimeout(fetchSyncStatus, 1000)
      } else {
        const error = await response.json()
        console.error("Sync failed:", error)
      }
    } catch (error) {
      console.error("Error triggering sync:", error)
    } finally {
      setSyncing(false)
      setSyncingResource(null)
    }
  }

  const canManageSync = session?.user?.role === "ADMIN"

  if (!canManageSync) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-blue-900 mb-4">Access Denied</h1>
          <p className="text-blue-600">You don't have permission to manage synchronization.</p>
        </div>
      </DashboardLayout>
    )
  }

  if (loading || !syncStatus) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="text-lg text-blue-900">Loading sync status...</div>
        </div>
      </DashboardLayout>
    )
  }

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600" />
    )
  }

  const getLastSyncTime = (resource: string) => {
    const stat = syncStatus.syncStats.find(s => s.resource === resource)
    return stat?._max.lastSyncTime ? new Date(stat._max.lastSyncTime).toLocaleString() : "Never"
  }

  const getTotalSyncs = (resource: string) => {
    const stat = syncStatus.syncStats.find(s => s.resource === resource)
    return stat?._count._all || 0
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-blue-900">Data Synchronization</h1>
            <p className="text-blue-600">Manage data sync between staff and student servers</p>
          </div>
          
          <Button 
            onClick={fetchSyncStatus} 
            variant="outline" 
            size="sm"
            className="border-blue-300 text-blue-700 hover:bg-blue-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </div>

        {/* Server Status */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-900">
                <Server className="h-5 w-5 mr-2" />
                Staff Server Connection
              </CardTitle>
            </CardHeader>
            <CardContent>
              {syncStatus.staffServerStatus ? (
                <div className="space-y-2">
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-green-700">Connected</span>
                  </div>
                  <div className="text-sm text-blue-700">
                    <div>Total Students: {syncStatus.staffServerStatus.totalStudents}</div>
                    <div>Synced: {syncStatus.staffServerStatus.syncedStudents}</div>
                    <div>Unsynced: {syncStatus.staffServerStatus.unsyncedStudents}</div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center">
                  <XCircle className="h-4 w-4 text-red-600 mr-2" />
                  <span className="text-red-700">Disconnected</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-900">
                <Database className="h-5 w-5 mr-2" />
                Local Database
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  <span className="text-green-700">Operational</span>
                </div>
                <div className="text-sm text-blue-700">
                  <div>Last Checked: {new Date(syncStatus.lastChecked).toLocaleString()}</div>
                  <div>Total Sync Operations: {syncStatus.recentSyncLogs.length}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sync Controls */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Manual Synchronization</CardTitle>
            <CardDescription>
              Trigger data synchronization manually or view automatic sync status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-blue-900">Student Data</h4>
                  <p className="text-sm text-blue-600">
                    Last sync: {getLastSyncTime("students")}
                  </p>
                  <p className="text-sm text-blue-600">
                    Total syncs: {getTotalSyncs("students")}
                  </p>
                </div>
                <Button 
                  onClick={() => triggerSync("students")}
                  disabled={syncing}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {syncing && syncingResource === "students" ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Sync className="h-4 w-4 mr-2" />
                  )}
                  Sync Students
                </Button>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-blue-900">Announcements</h4>
                  <p className="text-sm text-blue-600">
                    Last sync: {getLastSyncTime("announcements")}
                  </p>
                  <p className="text-sm text-blue-600">
                    Total syncs: {getTotalSyncs("announcements")}
                  </p>
                </div>
                <Button 
                  onClick={() => triggerSync("announcements")}
                  disabled={syncing}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {syncing && syncingResource === "announcements" ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Sync className="h-4 w-4 mr-2" />
                  )}
                  Sync Announcements
                </Button>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-blue-900">Full Sync</h4>
                  <p className="text-sm text-blue-600">
                    Sync all data types
                  </p>
                  <p className="text-sm text-blue-600">
                    Use for initial setup
                  </p>
                </div>
                <Button 
                  onClick={() => triggerSync("all")}
                  disabled={syncing}
                  className="w-full bg-orange-600 hover:bg-orange-700"
                >
                  {syncing && syncingResource === "all" ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Activity className="h-4 w-4 mr-2" />
                  )}
                  Full Sync
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Sync Logs */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Recent Sync Operations</CardTitle>
            <CardDescription>
              History of synchronization attempts and their results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border border-blue-200 rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow className="border-blue-200">
                    <TableHead className="text-blue-700">Status</TableHead>
                    <TableHead className="text-blue-700">Resource</TableHead>
                    <TableHead className="text-blue-700">Records</TableHead>
                    <TableHead className="text-blue-700">Time</TableHead>
                    <TableHead className="text-blue-700">Errors</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {syncStatus.recentSyncLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No sync operations found
                      </TableCell>
                    </TableRow>
                  ) : (
                    syncStatus.recentSyncLogs.map((log) => (
                      <TableRow key={log.id} className="border-blue-100">
                        <TableCell>
                          <div className="flex items-center">
                            {getStatusIcon(log.success)}
                            <span className={`ml-2 text-sm ${log.success ? 'text-green-700' : 'text-red-700'}`}>
                              {log.success ? 'Success' : 'Failed'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="capitalize text-blue-900">{log.resource}</span>
                        </TableCell>
                        <TableCell>
                          <span className="text-blue-900">{log.recordCount}</span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-blue-700">
                            {new Date(log.createdAt).toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          {log.errors && log.errors.length > 0 ? (
                            <div className="flex items-center">
                              <AlertTriangle className="h-4 w-4 text-red-600 mr-1" />
                              <span className="text-red-700 text-sm">
                                {log.errors.length} error(s)
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-400">None</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
