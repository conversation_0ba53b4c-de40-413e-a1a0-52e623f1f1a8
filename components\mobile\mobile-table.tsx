"use client"

import { ReactNode } from 'react'
import { cn } from '@/lib/utils/cn'
import { ChevronRight } from 'lucide-react'

interface MobileTableProps {
  children: ReactNode
  className?: string
}

interface MobileTableRowProps {
  children: ReactNode
  onClick?: () => void
  className?: string
}

interface MobileTableCellProps {
  label: string
  children: ReactNode
  className?: string
  primary?: boolean
}

export function MobileTable({ children, className }: MobileTableProps) {
  return (
    <div className={cn("lg:hidden space-y-3", className)}>
      {children}
    </div>
  )
}

export function MobileTableRow({ children, onClick, className }: MobileTableRowProps) {
  const Component = onClick ? 'button' : 'div'
  
  return (
    <Component
      onClick={onClick}
      className={cn(
        "w-full bg-white rounded-lg border border-blue-200 p-4 space-y-3",
        "touch-manipulation", // Better touch targets
        onClick && "hover:bg-blue-50 transition-colors",
        className
      )}
    >
      <div className="space-y-2">
        {children}
      </div>
      {onClick && (
        <div className="flex justify-end">
          <ChevronRight className="h-4 w-4 text-blue-400" />
        </div>
      )}
    </Component>
  )
}

export function MobileTableCell({ label, children, className, primary = false }: MobileTableCellProps) {
  return (
    <div className={cn("flex justify-between items-start", className)}>
      <span className={cn(
        "text-sm font-medium text-blue-600 min-w-0 flex-shrink-0 mr-3",
        primary && "text-blue-900"
      )}>
        {label}:
      </span>
      <div className={cn(
        "text-sm text-gray-900 text-right min-w-0 flex-1",
        primary && "font-semibold"
      )}>
        {children}
      </div>
    </div>
  )
}

// Example usage component
export function CourseMobileTable({ courses, onCourseClick }: {
  courses: Array<{
    id: string
    name: string
    teacher: string
    schedule: string
    progress: number
    status: string
  }>
  onCourseClick: (id: string) => void
}) {
  return (
    <MobileTable>
      {courses.map((course) => (
        <MobileTableRow
          key={course.id}
          onClick={() => onCourseClick(course.id)}
        >
          <MobileTableCell label="Course" primary>
            {course.name}
          </MobileTableCell>
          <MobileTableCell label="Teacher">
            {course.teacher}
          </MobileTableCell>
          <MobileTableCell label="Schedule">
            {course.schedule}
          </MobileTableCell>
          <MobileTableCell label="Progress">
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${course.progress}%` }}
                />
              </div>
              <span className="text-xs text-gray-500">{course.progress}%</span>
            </div>
          </MobileTableCell>
          <MobileTableCell label="Status">
            <span className={cn(
              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
              course.status === 'ACTIVE' && "bg-green-100 text-green-800",
              course.status === 'COMPLETED' && "bg-blue-100 text-blue-800",
              course.status === 'PAUSED' && "bg-yellow-100 text-yellow-800"
            )}>
              {course.status}
            </span>
          </MobileTableCell>
        </MobileTableRow>
      ))}
    </MobileTable>
  )
}
