import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const INTER_SERVER_SECRET = process.env.INTER_SERVER_SECRET || "your-secret-key"

const broadcastMessageSchema = z.object({
  id: z.string().min(1, "Message ID is required"),
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  recipientType: z.enum(["ALL", "STUDENTS", "SPECIFIC"]),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  senderReferenceId: z.string().min(1, "Sender reference ID is required"),
  senderName: z.string().min(1, "Sender name is required"),
  sentAt: z.string().optional(),
})

// Middleware to verify inter-server authentication
function verifyInterServerAuth(request: NextRequest) {
  const authHeader = request.headers.get("authorization")
  const token = authHeader?.replace("Bearer ", "")
  
  if (!token || token !== INTER_SERVER_SECRET) {
    return false
  }
  
  return true
}

export async function POST(request: NextRequest) {
  try {
    if (!verifyInterServerAuth(request)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = broadcastMessageSchema.parse(body)

    // Determine which students should receive this message
    let targetStudentIds: string[] = []

    if (validatedData.recipientType === "ALL" || validatedData.recipientType === "STUDENTS") {
      // Get all active students
      const students = await prisma.student.findMany({
        where: { 
          user: { 
            role: "STUDENT" 
          } 
        },
        select: { id: true }
      })
      targetStudentIds = students.map(s => s.id)
    } else if (validatedData.recipientType === "SPECIFIC" && validatedData.recipientIds) {
      // Use specific student IDs
      targetStudentIds = validatedData.recipientIds
    }

    // Create the message in the students database
    const message = await prisma.message.create({
      data: {
        id: validatedData.id,
        subject: validatedData.subject,
        content: validatedData.content,
        recipientType: validatedData.recipientType,
        recipientIds: targetStudentIds,
        priority: validatedData.priority,
        status: "SENT",
        sentAt: validatedData.sentAt ? new Date(validatedData.sentAt) : new Date(),
        senderReferenceId: validatedData.senderReferenceId,
      }
    })

    // Connect the message to the target students (for the many-to-many relation)
    if (targetStudentIds.length > 0) {
      const studentUsers = await prisma.user.findMany({
        where: {
          studentProfile: {
            id: { in: targetStudentIds }
          }
        },
        select: { id: true }
      })

      await prisma.message.update({
        where: { id: message.id },
        data: {
          recipients: {
            connect: studentUsers.map(user => ({ id: user.id }))
          }
        }
      })
    }

    return NextResponse.json({
      message: "Message broadcasted successfully",
      messageId: message.id,
      recipientCount: targetStudentIds.length
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error broadcasting message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
