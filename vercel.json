{"framework": "nextjs", "buildCommand": "prisma generate && npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/", "destination": "/auth/signin", "permanent": false}], "env": {"APP_NAME": "Innovative Centre - Student Portal"}}