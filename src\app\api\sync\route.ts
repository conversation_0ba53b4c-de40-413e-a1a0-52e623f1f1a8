import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { syncClient } from "@/lib/sync/client"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const syncRequestSchema = z.object({
  resource: z.enum(["students", "announcements", "all"]),
  force: z.boolean().default(false),
  studentId: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Only allow admin users or system to trigger sync
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { resource, force, studentId } = syncRequestSchema.parse(body)

    const results: any = {}

    if (resource === "students" || resource === "all") {
      console.log("Starting student data sync...")
      results.students = await syncClient.syncStudentData(studentId)
    }

    if (resource === "announcements" || resource === "all") {
      console.log("Starting announcements sync...")
      results.announcements = await syncClient.syncAnnouncements()
    }

    // Calculate overall success
    const allSuccessful = Object.values(results).every((result: any) => result.success)
    const totalSynced = Object.values(results).reduce((sum: number, result: any) => sum + result.syncedCount, 0)
    const allErrors = Object.values(results).flatMap((result: any) => result.errors)

    return NextResponse.json({
      success: allSuccessful,
      totalSynced,
      results,
      errors: allErrors,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Sync request failed:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get sync status and recent logs
    const recentSyncLogs = await prisma.syncLog.findMany({
      orderBy: { createdAt: "desc" },
      take: 10
    })

    const syncStats = await prisma.syncLog.groupBy({
      by: ['resource'],
      _count: {
        _all: true
      },
      _max: {
        lastSyncTime: true
      },
      where: {
        success: true
      }
    })

    // Get staff server sync status
    let staffServerStatus = null
    try {
      staffServerStatus = await syncClient.getSyncStatus()
    } catch (error) {
      console.error("Could not get staff server status:", error)
    }

    return NextResponse.json({
      recentSyncLogs: recentSyncLogs.map(log => ({
        id: log.id,
        resource: log.resource,
        action: log.action,
        recordCount: log.recordCount,
        success: log.success,
        errors: log.errors,
        lastSyncTime: log.lastSyncTime,
        createdAt: log.createdAt
      })),
      syncStats,
      staffServerStatus,
      lastChecked: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error getting sync status:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
