"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { 
  Clock, 
  CheckCircle, 
  XCircle,
  FileText,
  Play,
  Send,
  Award,
  BookOpen,
  Timer
} from "lucide-react"
import DashboardLayout from "@/components/dashboard/layout"

interface Assessment {
  id: string
  testName: string
  type: string
  level?: string
  score?: number
  maxScore: number
  passed: boolean
  questions?: Question[]
  assignedAt: string
  startedAt?: string
  completedAt?: string
  results?: any
}

interface Question {
  id: string
  question: string
  type: "multiple_choice" | "text" | "essay" | "listening" | "speaking"
  options?: string[]
  correctAnswer?: string
  points: number
}

const assessmentTypes = {
  LEVEL_TEST: "Level Test",
  PROGRESS_TEST: "Progress Test", 
  FINAL_EXAM: "Final Exam",
  GROUP_TEST: "Group Test"
}

const getStatusBadge = (assessment: Assessment) => {
  if (assessment.score !== null && assessment.score !== undefined) {
    return (
      <Badge variant={assessment.passed ? "default" : "destructive"}>
        {assessment.passed ? "Passed" : "Failed"} ({assessment.score}/{assessment.maxScore})
      </Badge>
    )
  } else if (assessment.completedAt) {
    return <Badge variant="secondary">Completed - Awaiting Grade</Badge>
  } else if (assessment.startedAt) {
    return <Badge variant="outline">In Progress</Badge>
  } else {
    return <Badge variant="secondary">Assigned</Badge>
  }
}

export default function StudentAssessmentsPage() {
  const { data: session } = useSession()
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null)

  useEffect(() => {
    fetchAssessments()
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (selectedAssessment && timeRemaining !== null && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => prev ? prev - 1 : 0)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [selectedAssessment, timeRemaining])

  const fetchAssessments = async () => {
    try {
      const response = await fetch("/api/assessments")
      if (response.ok) {
        const data = await response.json()
        setAssessments(data.assessments || [])
      }
    } catch (error) {
      console.error("Error fetching assessments:", error)
    } finally {
      setLoading(false)
    }
  }

  const startAssessment = async (assessment: Assessment) => {
    try {
      const response = await fetch(`/api/assessments/${assessment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "start",
          startedAt: new Date().toISOString()
        }),
      })

      if (response.ok) {
        const updatedAssessment = await response.json()
        setSelectedAssessment(updatedAssessment)
        setCurrentQuestionIndex(0)
        setAnswers({})
        
        // Set timer for 60 minutes (3600 seconds) if not already set
        if (!updatedAssessment.timeLimit) {
          setTimeRemaining(3600)
        }
      }
    } catch (error) {
      console.error("Error starting assessment:", error)
    }
  }

  const submitAssessment = async () => {
    if (!selectedAssessment) return

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/assessments/${selectedAssessment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "submit_answers",
          answers: Object.entries(answers).map(([questionId, answer]) => ({
            questionId,
            answer
          })),
          completedAt: new Date().toISOString()
        }),
      })

      if (response.ok) {
        await fetchAssessments()
        setSelectedAssessment(null)
        setAnswers({})
        setTimeRemaining(null)
      }
    } catch (error) {
      console.error("Error submitting assessment:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const pendingAssessments = assessments.filter(a => !a.startedAt)
  const inProgressAssessments = assessments.filter(a => a.startedAt && !a.completedAt)
  const completedAssessments = assessments.filter(a => a.completedAt)

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading assessments...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Assessments</h1>
          <p className="text-gray-600">View and complete your assigned assessments</p>
        </div>

        {/* Assessment Taking Dialog */}
        {selectedAssessment && (
          <Dialog open={!!selectedAssessment} onOpenChange={() => setSelectedAssessment(null)}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{selectedAssessment.testName}</DialogTitle>
                <DialogDescription>
                  {assessmentTypes[selectedAssessment.type as keyof typeof assessmentTypes]}
                  {selectedAssessment.level && ` - ${selectedAssessment.level}`}
                </DialogDescription>
              </DialogHeader>

              {timeRemaining !== null && (
                <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-lg">
                  <Timer className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">
                    Time Remaining: {formatTime(timeRemaining)}
                  </span>
                </div>
              )}

              {selectedAssessment.questions && selectedAssessment.questions.length > 0 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      Question {currentQuestionIndex + 1} of {selectedAssessment.questions.length}
                    </span>
                    <Progress 
                      value={((currentQuestionIndex + 1) / selectedAssessment.questions.length) * 100} 
                      className="w-32"
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-medium mb-2">
                        {selectedAssessment.questions[currentQuestionIndex].question}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Points: {selectedAssessment.questions[currentQuestionIndex].points}
                      </p>
                    </div>

                    {selectedAssessment.questions[currentQuestionIndex].type === "multiple_choice" && (
                      <RadioGroup
                        value={answers[selectedAssessment.questions[currentQuestionIndex].id] || ""}
                        onValueChange={(value) => setAnswers(prev => ({
                          ...prev,
                          [selectedAssessment.questions[currentQuestionIndex].id]: value
                        }))}
                      >
                        {selectedAssessment.questions[currentQuestionIndex].options?.map((option, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={`option-${index}`} />
                            <Label htmlFor={`option-${index}`}>{option}</Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}

                    {(selectedAssessment.questions[currentQuestionIndex].type === "text" || 
                      selectedAssessment.questions[currentQuestionIndex].type === "essay") && (
                      <Textarea
                        placeholder="Enter your answer..."
                        value={answers[selectedAssessment.questions[currentQuestionIndex].id] || ""}
                        onChange={(e) => setAnswers(prev => ({
                          ...prev,
                          [selectedAssessment.questions[currentQuestionIndex].id]: e.target.value
                        }))}
                        rows={selectedAssessment.questions[currentQuestionIndex].type === "essay" ? 6 : 3}
                      />
                    )}
                  </div>

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))}
                      disabled={currentQuestionIndex === 0}
                    >
                      Previous
                    </Button>

                    {currentQuestionIndex < selectedAssessment.questions.length - 1 ? (
                      <Button
                        onClick={() => setCurrentQuestionIndex(prev => prev + 1)}
                      >
                        Next
                      </Button>
                    ) : (
                      <Button
                        onClick={submitAssessment}
                        disabled={isSubmitting}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        {isSubmitting ? "Submitting..." : "Submit Assessment"}
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        )}

        {/* Pending Assessments */}
        {pendingAssessments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-orange-600" />
                Pending Assessments ({pendingAssessments.length})
              </CardTitle>
              <CardDescription>
                Assessments assigned to you that haven&apos;t been started yet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {pendingAssessments.map((assessment) => (
                  <div key={assessment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-medium">{assessment.testName}</h3>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>{assessmentTypes[assessment.type as keyof typeof assessmentTypes]}</span>
                        {assessment.level && <span>Level: {assessment.level}</span>}
                        <span>Max Score: {assessment.maxScore}</span>
                        <span>Assigned: {new Date(assessment.assignedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(assessment)}
                      <Button
                        onClick={() => startAssessment(assessment)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Start Assessment
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* In Progress Assessments */}
        {inProgressAssessments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-blue-600" />
                In Progress ({inProgressAssessments.length})
              </CardTitle>
              <CardDescription>
                Assessments you&apos;ve started but haven&apos;t completed yet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {inProgressAssessments.map((assessment) => (
                  <div key={assessment.id} className="flex items-center justify-between p-4 border rounded-lg bg-blue-50">
                    <div className="flex-1">
                      <h3 className="font-medium">{assessment.testName}</h3>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>{assessmentTypes[assessment.type as keyof typeof assessmentTypes]}</span>
                        {assessment.level && <span>Level: {assessment.level}</span>}
                        <span>Started: {assessment.startedAt ? new Date(assessment.startedAt).toLocaleString() : "Unknown"}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(assessment)}
                      <Button
                        onClick={() => setSelectedAssessment(assessment)}
                        size="sm"
                        variant="outline"
                      >
                        Continue
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Completed Assessments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 mr-2 text-green-600" />
              Completed Assessments ({completedAssessments.length})
            </CardTitle>
            <CardDescription>
              Your assessment history and results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {completedAssessments.length === 0 ? (
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No completed assessments</h3>
                <p className="text-gray-600">
                  Complete your pending assessments to see results here.
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {completedAssessments.map((assessment) => (
                  <div key={assessment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-medium">{assessment.testName}</h3>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>{assessmentTypes[assessment.type as keyof typeof assessmentTypes]}</span>
                        {assessment.level && <span>Level: {assessment.level}</span>}
                        <span>Completed: {assessment.completedAt ? new Date(assessment.completedAt).toLocaleDateString() : "Unknown"}</span>
                      </div>
                      {assessment.score !== null && assessment.score !== undefined && (
                        <div className="mt-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">Score:</span>
                            <Progress
                              value={(assessment.score / assessment.maxScore) * 100}
                              className="w-32"
                            />
                            <span className="text-sm">
                              {assessment.score}/{assessment.maxScore} ({Math.round((assessment.score / assessment.maxScore) * 100)}%)
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(assessment)}
                      <Button variant="ghost" size="sm">
                        <FileText className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Empty State */}
        {assessments.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No assessments assigned</h3>
                <p className="text-gray-600">
                  Your teacher will assign assessments that will appear here.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
