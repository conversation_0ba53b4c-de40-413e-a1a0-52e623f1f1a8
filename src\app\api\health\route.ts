import { NextRequest, NextResponse } from 'next/server'
import { getStaffClient } from '@/lib/api-clients/staff-client'

export async function GET(request: NextRequest) {
  const checks: Record<string, { status: string; message?: string; responseTime?: number }> = {}
  
  try {
    // Database check
    const dbStart = Date.now()
    try {
      // TODO: Add actual database connection check using Prisma
      // await prisma.$queryRaw`SELECT 1`
      checks.database = {
        status: 'ok',
        responseTime: Date.now() - dbStart
      }
    } catch (error) {
      checks.database = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Database connection failed',
        responseTime: Date.now() - dbStart
      }
    }

    // Inter-server communication check
    const interServerStart = Date.now()
    try {
      const staffClient = getStaffClient()
      const result = await staffClient.healthCheck()
      
      checks.interServerComm = {
        status: result.success ? 'ok' : 'error',
        message: result.success ? undefined : result.error,
        responseTime: Date.now() - interServerStart
      }
    } catch (error) {
      checks.interServerComm = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Inter-server communication failed',
        responseTime: Date.now() - interServerStart
      }
    }

    // PWA features check
    const pwaEnabled = process.env.ENABLE_PWA === 'true'
    checks.pwaFeatures = {
      status: pwaEnabled ? 'ok' : 'disabled',
      message: pwaEnabled ? 'PWA features enabled' : 'PWA features disabled'
    }

    // Service Worker check (basic validation)
    try {
      // Check if service worker file exists
      const swResponse = await fetch(`${request.nextUrl.origin}/sw.js`)
      checks.serviceWorker = {
        status: swResponse.ok ? 'ok' : 'error',
        message: swResponse.ok ? 'Service worker accessible' : 'Service worker not found'
      }
    } catch (error) {
      checks.serviceWorker = {
        status: 'error',
        message: 'Service worker check failed'
      }
    }

    // Overall health status
    const hasErrors = Object.values(checks).some(check => check.status === 'error')
    const overallStatus = hasErrors ? 'unhealthy' : 'healthy'

    const response = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      server: 'students',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      pwa: {
        enabled: pwaEnabled,
        manifestUrl: `${request.nextUrl.origin}/manifest.json`,
        serviceWorkerUrl: `${request.nextUrl.origin}/sw.js`
      },
      checks
    }

    return NextResponse.json(response, {
      status: hasErrors ? 503 : 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        server: 'students',
        error: error instanceof Error ? error.message : 'Unknown error',
        checks
      },
      { status: 503 }
    )
  }
}
